'use client';

import React, { useState, createContext, useContext, useEffect, useCallback } from 'react';
import { PayloadAuthClient, type PayloadAuthConfig } from './auth-client';
import type { AuthUser, LoginCredentials } from './types';

type Login = (args: LoginCredentials) => Promise<void>;
type Logout = () => Promise<void>;

type AuthContext = {
  user?: AuthUser | null;
  setUser: (user: AuthUser | null) => void;
  logout: Logout;
  login: Login;
  isLoading: boolean;
};

const Context = createContext({} as AuthContext);

export interface AuthProviderProps {
  children: React.ReactNode;
  config: PayloadAuthConfig;
}

/**
 * PayloadCMS Authentication Provider
 * Based on official PayloadCMS documentation pattern
 */
export const AuthProvider = ({ children, config }: AuthProviderProps): React.ReactElement => {
  const [user, setUser] = useState<AuthUser | null>();
  const [isLoading, setIsLoading] = useState(true);

  const authClient = new PayloadAuthClient(config);

  const login = useCallback<Login>(async (args) => {
    setIsLoading(true);
    try {
      const userData = await authClient.login(args);
      setUser(userData);
    } catch (error) {
      setUser(null);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [authClient]);

  const logout = useCallback<Logout>(async () => {
    setIsLoading(true);
    try {
      await authClient.logout();
    } catch (error) {
      // Ignore logout errors - we'll clear state anyway
      if (config.debug) {
        console.log('⚠️ Logout API error ignored:', error);
      }
    }

    // Always clear user state regardless of API response
    setUser(null);
    setIsLoading(false);
  }, [authClient, config.debug]);

  // Check authentication status on mount - DISABLED to prevent auto-signin loops
  useEffect(() => {
    // Just set loading to false and user to null initially
    setIsLoading(false);
    setUser(null);
  }, []);

  return (
    <Context.Provider
      value={{
        user,
        setUser,
        login,
        logout,
        isLoading,
      }}
    >
      {children}
    </Context.Provider>
  );
};

type UseAuth<T = AuthUser> = () => AuthContext;

export const useAuth: UseAuth = () => useContext(Context);

// Convenience hook for trainee authentication
export function useTraineeAuth() {
  const auth = useAuth();

  // Return auth context with additional computed properties
  return {
    ...auth,
    isAuthenticated: !!auth.user,
    isTrainee: auth.user?.role === 'trainee',
  };
}

// Convenience hook for admin authentication
export function useAdminAuth() {
  const auth = useAuth();

  // Return auth context with additional computed properties
  return {
    ...auth,
    isAuthenticated: !!auth.user,
    isAdmin: auth.user?.role === 'admin',
  };
}
