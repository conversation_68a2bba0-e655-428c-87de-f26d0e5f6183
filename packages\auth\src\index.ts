/**
 * PayloadCMS Authentication Package
 * Official pattern implementation based on PayloadCMS documentation
 */

// Types
export type {
  AuthUser,
  LoginCredentials,
} from './types';

// Core authentication client
export { PayloadAuthClient, type PayloadAuthConfig } from './auth-client';

// React Provider and hooks
export {
  AuthProvider,
  useAuth,
  useAdminAuth,
  useTraineeAuth,
  type AuthProviderProps,
} from './hooks';

// Note: Middleware and utilities removed in favor of PayloadCMS official pattern
// Authentication is now handled through PayloadCMS REST API with cookies
