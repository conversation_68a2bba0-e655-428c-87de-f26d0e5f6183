# Database Configuration
DATABASE_URI=postgresql://<USER>:<PASSWORD>@<HOST>:<PORT>/<DATABASE>

# Payload CMS Configuration
PAYLOAD_SECRET=<YOUR_PAYLOAD_SECRET>

# Environment
NODE_ENV=development



# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=<YOUR_SUPABASE_URL>
NEXT_PUBLIC_SUPABASE_ANON_KEY=<YOUR_SUPABASE_ANON_KEY>
SUPABASE_SERVICE_ROLE_KEY=<YOUR_SUPABASE_SERVICE_ROLE_KEY>

# Cloudinary Configuration (Client-side)
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=<YOUR_CLOUDINARY_CLOUD_NAME>

# Cloudinary Configuration (Server-side)
CLOUDINARY_API_KEY=<YOUR_CLOUDINARY_API_KEY>
CLOUDINARY_API_SECRET=<YOUR_CLOUDINARY_API_SECRET>