{"name": "@encreasl/auth", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --max-warnings 0", "type-check": "tsc --noEmit"}, "dependencies": {"zod": "^4.0.10"}, "devDependencies": {"@encreasl/eslint-config": "workspace:*", "@encreasl/typescript-config": "workspace:*", "@types/node": "^20", "@types/react": "^19", "eslint": "^9", "typescript": "^5"}, "peerDependencies": {"next": ">=14.0.0", "react": ">=18.0.0"}}