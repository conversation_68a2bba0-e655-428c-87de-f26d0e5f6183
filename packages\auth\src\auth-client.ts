/**
 * PayloadCMS Authentication Provider - Official Pattern
 * Based on: https://github.com/payloadcms/payload/discussions/1902
 */

import type { AuthUser, LoginCredentials } from './types';

export type Login = (args: LoginCredentials) => Promise<void>;
export type Logout = () => Promise<void>;

export interface PayloadAuthConfig {
  apiUrl: string;
  requiredRole?: string | string[];
  debug?: boolean;
}

export class PayloadAuthClient {
  private config: PayloadAuthConfig;

  constructor(config: PayloadAuthConfig) {
    this.config = {
      debug: false,
      ...config,
    };
  }

  /**
   * Login using PayloadCMS REST API
   */
  async login(credentials: LoginCredentials): Promise<AuthUser> {
    if (this.config.debug) {
      console.log('🔐 PayloadCMS login initiated...');
      console.log('📧 Email:', credentials.email);
      console.log('🌐 API URL:', this.config.apiUrl);
    }

    const res = await fetch(`${this.config.apiUrl}/users/login`, {
      method: 'POST',
      body: JSON.stringify(credentials),
      credentials: 'include', // Essential for PayloadCMS cookies
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!res.ok) {
      const errorData = await res.json().catch(() => ({}));
      throw new Error(errorData.message || 'Invalid login credentials');
    }

    const json = await res.json();

    if (!json.user) {
      throw new Error('No user data returned from login');
    }

    // Validate user is active
    if (json.user.isActive === false) {
      throw new Error('Account is inactive. Please contact administrator.');
    }

    // Validate user role if required
    if (this.config.requiredRole && !this.validateUserRole(json.user)) {
      const required = Array.isArray(this.config.requiredRole)
        ? this.config.requiredRole.join(' or ')
        : this.config.requiredRole;
      throw new Error(`Access denied. Required role: ${required}. Current role: ${json.user.role}`);
    }

    if (this.config.debug) {
      console.log('✅ PayloadCMS login successful:', {
        email: json.user.email,
        role: json.user.role,
        isActive: json.user.isActive,
      });
    }

    return json.user;
  }

  /**
   * Logout - Clear cookies and state (simple approach)
   */
  async logout(): Promise<void> {
    try {
      // Try to call PayloadCMS logout endpoint, but don't fail if it doesn't work
      await fetch(`${this.config.apiUrl}/users/logout`, {
        method: 'POST',
        credentials: 'include',
      }).catch(() => {
        // Ignore errors from logout endpoint
        if (this.config.debug) {
          console.log('⚠️ PayloadCMS logout endpoint failed, but continuing...');
        }
      });
    } catch (error) {
      // Ignore all logout API errors
    }

    // Clear all PayloadCMS cookies manually
    if (typeof window !== 'undefined') {
      // Clear common PayloadCMS cookie names
      const cookiesToClear = ['payload-token', 'payload-refresh-token', 'connect.sid'];

      cookiesToClear.forEach(cookieName => {
        document.cookie = `${cookieName}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; domain=${window.location.hostname}`;
        document.cookie = `${cookieName}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
      });
    }

    if (this.config.debug) {
      console.log('✅ Logout completed (cookies cleared)');
    }
  }

  /**
   * Get current user using PayloadCMS /me endpoint
   */
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const result = await fetch(`${this.config.apiUrl}/users/me`, {
        credentials: 'include',
      });

      if (!result.ok) {
        return null;
      }

      const json = await result.json();
      const user = json.user || null;

      // Validate role if user exists and role is required
      if (user && this.config.requiredRole && !this.validateUserRole(user)) {
        if (this.config.debug) {
          console.log('❌ User role validation failed:', user.role);
        }
        return null;
      }

      return user;
    } catch (error) {
      if (this.config.debug) {
        console.error('Failed to get current user:', error);
      }
      return null;
    }
  }

  /**
   * Validate user role against configuration
   */
  private validateUserRole(user: AuthUser): boolean {
    if (!this.config.requiredRole) return true;

    if (Array.isArray(this.config.requiredRole)) {
      return this.config.requiredRole.includes(user.role);
    }

    return user.role === this.config.requiredRole;
  }
}
